import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { CategoryIconComponent } from '../../../manage-order/components/category-icon/category-icon.component';

@Component({
  selector: 'pos-category-tile',
  standalone: true,
  templateUrl: './pos-category-tile.component.html',
  styleUrls: ['./pos-category-tile.component.scss'],
  imports: [CommonModule, CategoryIconComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PosCategoryTileComponent {
  @Input() name: string;
  @Input() iconName: string;
  @Input() isSelected: boolean = false;
  @Output() clicked = new EventEmitter();

  onPress() {
    this.clicked.emit(this.iconName);
  }
}
