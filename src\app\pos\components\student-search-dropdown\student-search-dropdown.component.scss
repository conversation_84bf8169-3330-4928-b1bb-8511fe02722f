.student-search-container {
  width: 100%;
  margin-bottom: 0rem;
}

.search-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #333;
  font-size: 0.9rem;
}

.student-search-field {
  width: 100%;
  
  .mat-form-field-wrapper {
    padding-bottom: 0;
  }
}

.student-search-input {
  font-size: 1rem;
}

.loading-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-icon {
  cursor: pointer;
  color: #666;
  
  &:hover {
    color: #333;
  }
}

.search-icon {
  color: #666;
}

.student-autocomplete {
  .mat-option {
    height: auto;
    min-height: 48px;
    padding: 8px 16px;
    line-height: 1.4;
  }
}

.student-option {
  &:hover {
    background-color: #f5f5f5;
  }
}

.student-option-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.student-name {
  font-weight: 500;
  font-size: 1rem;
  color: #333;
  margin-bottom: 2px;
}

.student-details {
  font-size: 0.85rem;
  color: #666;
}

.no-results {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 8px;
}

// Responsive adjustments
@media (max-width: 768px) {
  .student-search-field {
    .mat-form-field-infix {
      font-size: 16px; // Prevents zoom on iOS
    }
  }
  
  .student-name {
    font-size: 0.95rem;
  }
  
  .student-details {
    font-size: 0.8rem;
  }
}
