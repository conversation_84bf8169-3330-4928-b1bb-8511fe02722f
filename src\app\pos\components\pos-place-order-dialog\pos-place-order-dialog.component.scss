.pos-place-order-dialog {
  min-width: 500px;
  max-width: 600px;
  padding: 0;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;

    p {
      margin-top: 16px;
      color: #666;
      font-size: 14px;
    }
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e0e0e0;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }

    .close-button {
      color: #666;
      
      &:hover {
        color: #333;
      }
    }
  }

  .student-info-section {
    padding: 16px 24px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;

    .student-card {
      display: flex;
      align-items: center;
      gap: 12px;

      mat-icon {
        color: #2196f3;
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      .student-details {
        h4 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .student-meta {
          margin: 0;
          font-size: 12px;
          color: #666;

          .guest-badge {
            background-color: #ff9800;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .payment-method-section {
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;

    h3 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .payment-method-display {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background-color: #f5f5f5;
      border-radius: 6px;

      mat-icon {
        color: #4caf50;
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      span {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }
    }
  }

  .wallet-balance-section {
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;

    .balance-card {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e0e0e0;

      .balance-info {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .balance-label {
          font-size: 12px;
          color: #666;
          font-weight: 500;
        }

        .balance-amount {
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }
      }

      .balance-status {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        font-weight: 500;

        &.sufficient {
          color: #4caf50;
        }

        &.insufficient {
          color: #f44336;
        }

        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .order-summary-section {
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;

    h3 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .summary-details {
      .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        font-size: 14px;

        &:not(:last-child) {
          border-bottom: 1px solid #f0f0f0;
        }

        &.total-row {
          font-weight: 600;
          font-size: 16px;
          color: #333;
          border-top: 2px solid #e0e0e0;
          padding-top: 12px;
          margin-top: 8px;
        }

        &.difference-row {
          font-weight: 500;

          &.positive {
            color: #f44336;
          }

          &.negative {
            color: #4caf50;
          }
        }
      }
    }
  }

  .error-section {
    padding: 16px 24px;
    background-color: #ffebee;
    border-left: 4px solid #f44336;

    mat-error {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-size: 14px;
      color: #d32f2f;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }

  .action-buttons {
    padding: 20px 24px;

    .button-row {
      display: flex;
      justify-content: center;
      gap: 12px;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .place-order-button,
      .top-up-button {
        min-width: 200px;
        height: 44px;
        font-size: 14px;
        font-weight: 600;
        border-radius: 6px;

        mat-spinner {
          margin-right: 8px;
        }
      }

      .cancel-button {
        min-width: 120px;
        height: 36px;
        font-size: 14px;
        color: #666;

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 600px) {
  .pos-place-order-dialog {
    min-width: 90vw;
    max-width: 90vw;

    .dialog-header,
    .student-info-section,
    .payment-method-section,
    .wallet-balance-section,
    .order-summary-section,
    .action-buttons {
      padding-left: 16px;
      padding-right: 16px;
    }

    .action-buttons {
      .button-row {
        flex-direction: column;
        align-items: stretch;

        .place-order-button,
        .top-up-button,
        .cancel-button {
          width: 100%;
        }
      }
    }
  }
}
