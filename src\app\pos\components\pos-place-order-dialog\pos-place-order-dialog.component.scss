@import '../../../../styles/cashless-font.scss';
@import '../../../../styles/cashless-theme.scss';
@import '../../../../styles/cashless-breakpoints.scss';

.pos-order-dialog {
  width: 100%;
  max-width: 500px;
  min-width: 400px;
  padding: 0;
  position: relative;

  .close-button-container {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;

    .close-button {
      color: #ff6b35;

      &:hover {
        background-color: rgba(255, 107, 53, 0.1);
      }
    }
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;

    p {
      margin-top: 16px;
      color: #666;
      font-size: 14px;
    }
  }

  .order-content {
    padding: 20px 24px 24px;

    .dialog-title {
      text-align: center;
      font-size: 24px;
      font-weight: 600;
      color: #666;
      margin: 0 0 20px 0;
      padding-top: 20px;
    }

    // Expansion Panel Styling
    mat-accordion {
      margin-bottom: 20px;

      mat-expansion-panel {
        box-shadow: none;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .mat-expansion-panel-header {
          padding: 12px 16px;
          height: auto;
          min-height: 48px;

          .mat-expansion-panel-header-title {
            .panel-title {
              font-size: 16px;
              font-weight: 600;
              color: #333;

              .amount-highlight {
                color: #ff6b35;
                font-weight: 700;
              }
            }
          }
        }

        .panel-content {
          padding: 0 16px 16px;

          .order-item,
          .fee-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            font-size: 14px;
            color: #666;

            &:not(:last-child) {
              border-bottom: 1px solid #f0f0f0;
            }

            .amount-highlight {
              color: #ff6b35;
              font-weight: 600;
            }
          }
        }
      }
    }

    // Edit Summary
    .edit-summary {
      margin-bottom: 20px;

      .summary-line {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;

        .amount-highlight {
          color: #ff6b35;
          font-weight: 700;
        }
      }
    }

    // Total Section
    .total-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      margin: 20px 0;
      border-top: 2px solid #e0e0e0;
      border-bottom: 1px solid #e0e0e0;

      .total-label {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .total-amount {
        font-size: 18px;
        font-weight: 700;
        color: #ff6b35;
      }
    }

    // Wallet Section
    .wallet-section {
      margin: 20px 0;

      .wallet-balance {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;

        .amount-highlight {
          color: #ff6b35;
          font-weight: 700;
        }
      }

      .wallet-description {
        font-size: 14px;
        color: #666;
        font-style: italic;
        line-height: 1.4;
      }
    }

    // Error Message
    .error-message {
      margin: 16px 0;
      padding: 12px;
      background-color: #ffebee;
      border-left: 4px solid #f44336;
      border-radius: 4px;

      mat-error {
        margin: 0;
        font-size: 14px;
        color: #d32f2f;
      }
    }

    // Action Buttons
    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 12px;
      margin-top: 24px;

      .go-back-btn {
        min-width: 120px;
        height: 40px;
        font-size: 14px;
        font-weight: 500;
        color: #666;
        border-color: #ddd;

        &:hover {
          background-color: #f5f5f5;
          border-color: #ccc;
        }
      }

      .confirm-btn {
        min-width: 200px;
        height: 40px;
        font-size: 14px;
        font-weight: 600;
        background-color: #ff6b35;
        color: white;

        &:hover:not(:disabled) {
          background-color: #e55a2b;
        }

        &:disabled {
          background-color: #ccc;
          color: #999;
        }

        mat-spinner {
          margin-right: 8px;
        }
      }
    }
  }

  .processing-state {
    padding: 20px 24px;

    .dialog-title {
      text-align: center;
      font-size: 24px;
      font-weight: 600;
      color: #666;
      margin: 0 0 20px 0;
    }
  }
}

// Responsive design
@media (max-width: 600px) {
  .pos-order-dialog {
    min-width: 90vw;
    max-width: 90vw;

    .order-content {
      padding: 16px 20px 20px;

      .action-buttons {
        flex-direction: column;
        align-items: stretch;

        .go-back-btn,
        .confirm-btn {
          width: 100%;
          margin-bottom: 8px;
        }
      }
    }
  }
}
