import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

// Angular Material
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatDialogModule } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';

// Routing
import { PosRoutingModule } from './pos-routing.module';

// Shared Modules
import { SharedModule } from '../shared/shared.module';
import { SharedToolsModule } from '../shared-tools/shared-tools.module';
import { SchoolsFormModule } from '../schools-form/schools-form.module';
import { SchoolsButtonModule } from '../schools-button/schools-button.module';
import { SchoolsCommonModule } from '../schools-common/schools-common.module';
import { ManageOrderModule } from '../manage-order/manage-order.module';

// Standalone Components
import { CategoryTileComponent } from '../manage-order/components/category-tile/category-tile.component';

// Components
import {
  PosComponent,
  PosTabComponent,
  StudentSearchDropdownComponent,
  PosCategoryTileComponent,
  PosProductItemComponent,
  DialogPosProductItem,
} from './components';

@NgModule({
  declarations: [
    PosComponent,
    PosTabComponent,
    StudentSearchDropdownComponent,
    PosProductItemComponent,
    DialogPosProductItem,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    PosRoutingModule,
    
    // Angular Material
    MatToolbarModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatAutocompleteModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatDialogModule,
    MatMenuModule,
    MatExpansionModule,
    MatBottomSheetModule,
    MatCheckboxModule,
    MatTooltipModule,
    
    // Shared Modules
    SharedModule,
    SharedToolsModule,
    SchoolsFormModule,
    SchoolsButtonModule,
    SchoolsCommonModule,
    ManageOrderModule,

    // Standalone Components
    CategoryTileComponent,
    PosCategoryTileComponent,
  ],
})
export class PosModule {}
