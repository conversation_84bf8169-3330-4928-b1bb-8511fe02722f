import {
  Component,
  OnInit,
  Output,
  Input,
  EventEmitter,
  Inject,
  ViewEncapsulation,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { environment } from '../../../../environments/environment';
import { DeviceDetectorService } from 'ngx-device-detector';
import * as moment from 'moment';

// Models
import {
  MenuItem,
  ImageUrlEnum,
  Days,
  BaseComponent,
  RemainingStockResponse,
  Category,
  ItemCutOffTimeTypes,
  Roles,
  MenuTypeEnum,
  RefinedSelectedOption,
  RefinedSelectedOptionFilter,
  RefinedOrderItem,
  SubOption,
  Option,
} from 'src/app/sharedModels';

// services
import {
  StockApiService,
  CashlessAppInsightsService,
  DateTimeService,
  UserService,
} from 'src/app/sharedServices';
import { MenuItemAvailability } from '../../../sharedModels';

interface PopupData {
  item: MenuItem;
  date: Date;
  category: Category;
}

@Component({
  selector: 'pos-product-item',
  templateUrl: './pos-product-item.component.html',
  styleUrls: ['./pos-product-item.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class PosProductItemComponent implements OnInit, OnChanges {
  @Input() category: Category;
  @Input() item: MenuItem;
  @Input() dateOrder: Date;
  @Input() schoolCutOffTime: string;
  @Input() currentMenuType: string;

  @Output() clickItem: EventEmitter<RefinedOrderItem> = new EventEmitter<RefinedOrderItem>();
  @Output() itemDialogOpened: EventEmitter<any> = new EventEmitter<any>();
  @Output() itemDialogClosed: EventEmitter<any> = new EventEmitter<any>();
  private fullScreenDialog: boolean = false;
  itemAvailable: boolean = true;
  availabilityText: string = '';
  isDayAvailable: boolean = true;
  ImageUrl: string;
  imageLoadError: boolean = false;
  defaultImagePath: string = 'assets/images/spriggy-default-image.png';

  constructor(
    public dialog: MatDialog,
    private deviceService: DeviceDetectorService,
    private userService: UserService,
    private appInsightsService: CashlessAppInsightsService,
    private dateTimeHelper: DateTimeService
  ) {}

  ngOnInit(): void {
    this.addOptionIdToAllItemSubOptions();

    // Get Image URL
    if (this.item.Images && this.item.Images.length > 0) {
      this.ImageUrl = this.item.Images[0].ImageUrl;
    } else if (this.item.ImageUrl) {
      this.ImageUrl = this.item.ImageUrl;
    }
  }

  addOptionIdToAllItemSubOptions(): void {
    if (this.item.Options) {
      this.item.Options.forEach((option: Option) => {
        if (option.SubOptions) {
          option.SubOptions.forEach((subOption: SubOption) => {
            // SubOption already has MenuItemOptionsCategoryId which links to the option
          });
        }
      });
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    for (const propName in changes) {
      if (changes.hasOwnProperty(propName)) {
        switch (propName) {
          case 'dateOrder': {
            // Check if item available
            if (this.dateOrder) {
              this.itemAvailable = true;
              if (this.item.Availabilities) {
                this.availabilityText = 'Available Dates: ';
                for (let index = 0; index < this.item.Availabilities.length; index++) {
                  const availability: MenuItemAvailability = this.item.Availabilities[index];
                  // Should work with different years
                  if (
                    moment(this.dateOrder).dayOfYear() >= moment(availability.StartDate).dayOfYear() &&
                    moment(this.dateOrder).dayOfYear() <= moment(availability.EndDate).dayOfYear()
                  ) {
                    this.itemAvailable = true;
                    this.isDayAvailable = true;
                    this.availabilityText = '';
                    break;
                  } else {
                    this.itemAvailable = false;
                    this.isDayAvailable = false;
                    this.availabilityText += moment(availability.StartDate).format('DD/MM') + ' - ' + moment(availability.EndDate).format('DD/MM') + ' ';
                  }
                }
              }

              // Check if item is available for the day
              if (this.item.AvailabilityDays) {
                const daysHelper = new Days(this.item.AvailabilityDays);
                this.isDayAvailable = daysHelper.IsAvailable(this.dateOrder);
                if (!this.isDayAvailable) {
                  this.itemAvailable = false;
                  this.availabilityText = 'Not available on ' + moment(this.dateOrder).format('dddd');
                }
              }

              // Check cut off time
              if (this.schoolCutOffTime && this.item.CutOffTimeType) {
                const currentTime = moment();
                const orderDate = moment(this.dateOrder);

                if (this.item.CutOffTimeType === ItemCutOffTimeTypes.Early) {
                  // For early cut off, check if current time is past the cut off
                  const cutOffTime = moment(this.schoolCutOffTime, 'HH:mm');
                  if (orderDate.isSame(currentTime, 'day') && currentTime.isAfter(cutOffTime)) {
                    this.itemAvailable = false;
                    this.availabilityText = 'Cut off time has passed';
                  }
                } else if (this.item.CutOffTimeType === ItemCutOffTimeTypes.Late) {
                  // For late cut off, check if current time is past the late cut off
                  const cutOffTime = moment(this.schoolCutOffTime, 'HH:mm');
                  const cutOffDateTime = moment(orderDate).subtract(1, 'day').hour(cutOffTime.hour()).minute(cutOffTime.minute());
                  if (currentTime.isAfter(cutOffDateTime)) {
                    this.itemAvailable = false;
                    this.availabilityText = 'Cut off time has passed';
                  }
                }
              }

              // Check menu type availability - simplified for now
              // Note: MenuItem doesn't have MenuType property in the current interface
              // This check would need to be implemented based on the actual menu structure
            }
            break;
          }
        }
      }
    }
  }

  GetUrlImage(): string {
    if (this.imageLoadError) {
      return this.defaultImagePath;
    }

    if (this.ImageUrl) {
      if (this.ImageUrl.indexOf('https:') > -1) {
        return this.ImageUrl;
      } else {
        return environment.blobStorage + ImageUrlEnum.ItemsSM + this.ImageUrl;
      }
    } else {
      return this.defaultImagePath;
    }
  }

  onImageError(): void {
    this.imageLoadError = true;
  }

  openDialogItem(): void {
    if (!this.itemAvailable) {
      return;
    }

    this.itemDialogOpened.emit();

    const isMobile = this.deviceService.isMobile();
    this.fullScreenDialog = isMobile;

    const dialogRef = this.dialog.open(DialogPosProductItem, {
      width: isMobile ? '100vw' : '600px',
      height: isMobile ? '100vh' : 'auto',
      maxWidth: isMobile ? '100vw' : '600px',
      maxHeight: isMobile ? '100vh' : '90vh',
      panelClass: isMobile ? 'full-screen-dialog' : 'custom-dialog-container',
      data: {
        item: this.item,
        date: this.dateOrder,
        category: this.category,
      },
    });

    dialogRef.afterClosed().subscribe((result: RefinedOrderItem) => {
      this.itemDialogClosed.emit();
      if (result) {
        this.clickItem.emit(result);
        // Log event - simplified for now
        // this.appInsightsService.logEvent('Item Added to Cart', {
        //   itemName: result.Name,
        //   itemPrice: result.ItemPriceIncGst,
        //   quantity: result.Quantity,
        // });
      }
    });
  }

  convertMenuItemToOrderItem(item: MenuItem, options: RefinedSelectedOption[]): RefinedOrderItem {
    return {
      MenuItemId: item.MenuItemId,
      OrderItemId: null,
      Name: item.Name,
      ItemPriceIncGst: item.Price,
      SelectedOptions: options,
      Quantity: item.Quantity,
      Printed: null,
      PrintedDate: null,
    };
  }
}

@Component({
  selector: 'dialog-pos-product-item',
  templateUrl: 'dialog-pos-product-item.html',
  styleUrls: ['./pos-product-item.component.scss'],
})
export class DialogPosProductItem extends BaseComponent implements OnInit {
  isMobile: boolean = false;
  hideButtonQuantity: boolean = false;
  maxQuantity: number = 5;
  imageUrl: string;
  hasManyImages: boolean = false;
  checkingAvailability: boolean = false;
  IsAvailable: boolean = true;
  selectedOptionsList: RefinedSelectedOptionFilter[] = [];
  imageLoadError: boolean = false;
  previewImageErrors: { [key: string]: boolean } = {};
  defaultImagePath: string = 'assets/images/spriggy-default-image1.jpeg';

  item: MenuItem;
  dateOrder: Date;
  constructor(
    public dialogRef: MatDialogRef<DialogPosProductItem>,
    @Inject(MAT_DIALOG_DATA) public data: PopupData,
    private deviceService: DeviceDetectorService,
    private stockAPIService: StockApiService,
    private userService: UserService
  ) {
    super();
  }

  ngOnInit(): void {
    this.item = Object.assign(new MenuItem(), { ...this.data.item });
    this.item.CategoryId = this.data.category.CategoryId;
    this.item.CatName = this.data.category.CatName;

    this.dateOrder = this.data.date;

    // init data
    this.InitItemData();

    // checking stocks
    if (this.item.IntegrationCode || (this.item.Stocks && this.item.Stocks[0].IsActive)) {
      this.checkingAvailability = true;

      let stockId;

      if (this.item.Stocks && this.item.Stocks.length > 0) {
        stockId = this.item.Stocks[0].StockId;
      }

      if (this.item.IntegrationCode) {
        stockId = null;
      }

      // get the remaining quantity for this item
      this.stockAPIService
        .GetRemainingQuantityStock(stockId, this.dateOrder, this.item.CanteenId, this.item.IntegrationCode)
        .subscribe({
          next: (response: RemainingStockResponse) => {
            if (response?.RemainingQuantity) {
              if (response.RemainingQuantity > this.maxQuantity) {
                this.IsAvailable = true;
              } else if (response.RemainingQuantity <= this.maxQuantity && response.RemainingQuantity > 0) {
                this.IsAvailable = true;
                this.maxQuantity = response.RemainingQuantity;
                this.item.MaxQuantity = response.RemainingQuantity;
              } else {
                this.IsAvailable = false;
              }
            }

            this.checkingAvailability = false;
          },
          error: error => {
            this.IsAvailable = false;
            this.checkingAvailability = false;
            this.handleErrorFromService(error);
          },
        });
    }
  }

  InitItemData(): void {
    this.isMobile = this.deviceService.isMobile();

    // Set default quantity
    if (!this.item.Quantity || this.item.Quantity <= 0) {
      this.item.Quantity = 1;
    }

    // Set image URL
    if (this.item.Images && this.item.Images.length > 0) {
      this.imageUrl = this.item.Images[0].ImageUrl;
      this.hasManyImages = this.item.Images.length > 1;
    } else if (this.item.ImageUrl) {
      this.imageUrl = this.item.ImageUrl;
    }

    // Initialize options
    if (this.item.Options) {
      this.item.Options.forEach((option: Option) => {
        if (option.IsRequired) {
          const selectedOption: RefinedSelectedOptionFilter = {
            MenuItemOptionId: 0,
            MenuItemOptionsCategoryId: option.MenuItemOptionsCategoryId,
            OptionName: option.Name,
            OptionCost: 0,
            PrintOnLabel: false,
            OptionType: '',
            Remove: false,
          };
          this.selectedOptionsList.push(selectedOption);
        }
      });
    }

    // Check if user has permission to change quantity
    const user = this.userService.GetUserConnected();
    this.hideButtonQuantity = user?.Role === Roles.Child;
  }

  OptionChanged(selectedOption: RefinedSelectedOptionFilter): void {
    const existingOptionIndex = this.selectedOptionsList.findIndex(x => x.MenuItemOptionsCategoryId === selectedOption.MenuItemOptionsCategoryId);
    if (existingOptionIndex > -1) {
      this.selectedOptionsList[existingOptionIndex] = selectedOption;
    } else {
      this.selectedOptionsList.push(selectedOption);
    }
  }

  CheckButtonDisable(): boolean {
    if (!this.IsAvailable) {
      return true;
    }

    // Check required options
    for (const option of this.item.Options || []) {
      if (option.IsRequired) {
        const selectedOption = this.selectedOptionsList.find(x => x.MenuItemOptionsCategoryId === option.MenuItemOptionsCategoryId);
        if (!selectedOption) {
          return true;
        }
      }
    }

    return false;
  }

  convertSelectedOptionFilterToSelectedOption(selectedOptionsList: RefinedSelectedOptionFilter[]): RefinedSelectedOption[] {
    return selectedOptionsList.map(selectedOption => ({
      MenuItemOptionId: selectedOption.MenuItemOptionId,
      MenuItemOptionsCategoryId: selectedOption.MenuItemOptionsCategoryId,
      OptionName: selectedOption.OptionName,
      OptionCost: selectedOption.OptionCost,
      PrintOnLabel: selectedOption.PrintOnLabel,
    }));
  }

  convertMenuItemToOrderItem(item: MenuItem, options: RefinedSelectedOption[]): RefinedOrderItem {
    return {
      MenuItemId: item.MenuItemId,
      OrderItemId: null,
      Name: item.Name,
      ItemPriceIncGst: item.Price,
      SelectedOptions: options,
      Quantity: item.Quantity,
      Printed: null,
      PrintedDate: null,
    };
  }

  closeDialog(item: MenuItem = null): void {
    const options = this.convertSelectedOptionFilterToSelectedOption(this.selectedOptionsList);
    let orderItem = this.convertMenuItemToOrderItem(item, options);
    this.dialogRef.close(orderItem);
  }

  MinusClick(): void {
    if (this.item.Quantity > 1) {
      this.item.Quantity--;
    }
  }

  PlusClick(): void {
    if (this.item.Quantity < this.maxQuantity) {
      this.item.Quantity++;
    }
  }

  GetUrlImage(): string {
    if (this.imageLoadError) {
      return this.defaultImagePath;
    }

    if (this.imageUrl.indexOf('https:') > -1) {
      return this.imageUrl;
    } else {
      return environment.blobStorage + ImageUrlEnum.ItemsLG + this.imageUrl;
    }
  }

  GetPreviewUrl(imageUrl: string): string {
    if (this.previewImageErrors[imageUrl]) {
      return this.defaultImagePath;
    }

    if (imageUrl.indexOf('https:') > -1) {
      return imageUrl;
    } else {
      return environment.blobStorage + ImageUrlEnum.ItemsSM + imageUrl;
    }
  }

  ClickPreview(imageUrl: string): void {
    this.imageUrl = imageUrl;
    this.imageLoadError = false;
  }

  onMainImageError(): void {
    this.imageLoadError = true;
  }

  onPreviewImageError(imageUrl: string): void {
    this.previewImageErrors[imageUrl] = true;
  }
}
