<mat-dialog-content>
  <div class="pos-place-order-dialog">
    <!-- Loading State -->
    <div *ngIf="summaryLoading" class="loading-container">
      <mat-spinner diameter="50"></mat-spinner>
      <p>Calculating order total...</p>
    </div>

    <!-- Order Summary -->
    <ng-container *ngIf="!summaryLoading && !orderPlaced">
      <!-- Header -->
      <div class="dialog-header">
        <h2 mat-dialog-title>
          <span *ngIf="!editOrderId">Place Order</span>
          <span *ngIf="editOrderId">Edit Order</span>
        </h2>
        <button mat-icon-button [mat-dialog-close]="false" class="close-button">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <!-- Student Information -->
      <div class="student-info-section" *ngIf="selectedStudent">
        <div class="student-card">
          <mat-icon>person</mat-icon>
          <div class="student-details">
            <h4>{{ selectedStudent.FirstName }} {{ selectedStudent.Lastname }}</h4>
            <p class="student-meta">
              <span *ngIf="selectedStudent.IsGuest" class="guest-badge">Guest User</span>
              <span *ngIf="!selectedStudent.IsGuest">Student ID: {{ selectedStudent.UserId }}</span>
            </p>
          </div>
        </div>
      </div>

      <!-- Payment Method Information -->
      <div class="payment-method-section">
        <h3>Payment Method</h3>
        <div class="payment-method-display">
          <mat-icon>credit_card</mat-icon>
          <span>Pay with Spriggy Card / Wallet</span>
        </div>
      </div>

      <!-- Wallet Balance (for Spriggy payments) -->
      <div class="wallet-balance-section" *ngIf="showWalletBalance()">
        <div class="balance-card">
          <div class="balance-info">
            <span class="balance-label">Current Balance:</span>
            <span class="balance-amount">{{ accountBalance | currency }}</span>
          </div>
          <div class="balance-status" [ngClass]="{
            'sufficient': sufficientWalletBalance,
            'insufficient': !sufficientWalletBalance
          }">
            <mat-icon *ngIf="sufficientWalletBalance">check_circle</mat-icon>
            <mat-icon *ngIf="!sufficientWalletBalance">warning</mat-icon>
            <span *ngIf="sufficientWalletBalance">Sufficient funds</span>
            <span *ngIf="!sufficientWalletBalance">Insufficient funds</span>
          </div>
        </div>
      </div>

      <!-- Order Summary -->
      <div class="order-summary-section">
        <h3>Order Summary</h3>
        
        <!-- Create Order Summary -->
        <div *ngIf="createOrderSummary && !editOrderId" class="summary-details">
          <div class="summary-row">
            <span>Subtotal:</span>
            <span>{{ createOrderSummary.totalAmount | currency }}</span>
          </div>
          <div class="summary-row total-row">
            <span>Total:</span>
            <span>{{ createOrderSummary.totalAmount | currency }}</span>
          </div>
        </div>

        <!-- Edit Order Summary -->
        <div *ngIf="editOrderSummary && editOrderId" class="summary-details">
          <div class="summary-row">
            <span>Previous Total:</span>
            <span>{{ editOrderSummary.previousPrice | currency }}</span>
          </div>
          <div class="summary-row">
            <span>New Total:</span>
            <span>{{ editOrderSummary.price | currency }}</span>
          </div>
          <div class="summary-row difference-row" [ngClass]="{
            'positive': editOrderSummary.priceDiff > 0,
            'negative': editOrderSummary.priceDiff < 0
          }">
            <span>Difference:</span>
            <span>{{ editOrderSummary.priceDiff | currency }}</span>
          </div>
          <div class="summary-row total-row">
            <span>Final Total:</span>
            <span>{{ editOrderSummary.price | currency }}</span>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div class="error-section" *ngIf="errorMessage">
        <mat-error>
          <mat-icon>error</mat-icon>
          {{ errorMessage }}
        </mat-error>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <div class="button-row">
          <!-- Top Up Button (for insufficient funds) -->
          <ng-container *ngIf="needToTopUp(); else confirmOrderButton">
            <button
              type="button"
              mat-raised-button
              color="primary"
              (click)="TopUpClick()"
              [disabled]="!topUpAmount"
              class="top-up-button"
            >
              {{ topUpAmount | currency }} Top up
            </button>
          </ng-container>

          <!-- Place Order Button -->
          <ng-template #confirmOrderButton>
            <button
              type="button"
              mat-raised-button
              color="primary"
              (click)="confirmOrder()"
              [disabled]="canteenOrAdminInsufficientWalletBalance || buttonLoading"
              class="place-order-button"
            >
              <mat-spinner *ngIf="buttonLoading" diameter="20"></mat-spinner>
              <span *ngIf="!buttonLoading">
                {{ editOrderId > 0 ? 'Update Order' : 'Place Order' }} ({{ totalPrice | currency }})
              </span>
              <span *ngIf="buttonLoading">Processing...</span>
            </button>
          </ng-template>
        </div>

        <!-- Cancel Button -->
        <div class="button-row">
          <button
            type="button"
            mat-button
            [mat-dialog-close]="false"
            [disabled]="buttonLoading"
            class="cancel-button"
          >
            Cancel
          </button>
        </div>
      </div>
    </ng-container>

    <!-- Order Placed Success -->
    <pos-orders-placed 
      *ngIf="orderPlaced" 
      [orderId]="placedOrderId"
      [paymentMethod]="paymentMethod"
      [viewType]="viewType"
      (goToOrders)="GotToOrders()">
    </pos-orders-placed>
  </div>
</mat-dialog-content>
