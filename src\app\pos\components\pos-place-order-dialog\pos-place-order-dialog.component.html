<mat-dialog-content>
  <div class="pos-order-dialog">
    <!-- Close Button -->
    <div class="close-button-container">
      <button mat-icon-button class="close-button" (click)="closeDialog()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <!-- Loading State -->
    <div *ngIf="summaryLoading" class="loading-state">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Calculating order total...</p>
    </div>

    <!-- Order Details -->
    <div *ngIf="!summaryLoading && !orderPlaced && !isProcessing" class="order-content">
      <!-- Header -->
      <h2 class="dialog-title">Order Details</h2>

      <!-- Order Summary Expansion Panel -->
      <mat-accordion *ngIf="createOrderSummary">
        <mat-expansion-panel [expanded]="true">
          <mat-expansion-panel-header>
            <mat-panel-title>
              <span class="panel-title">Orders: (<span class="amount-highlight">{{ createOrderSummary.totalAmount | currency }}</span>)</span>
            </mat-panel-title>
          </mat-expansion-panel-header>
          <div class="panel-content">
            <div *ngFor="let order of createOrderSummary.createOrdersInfo" class="order-item">
              {{ order.studentName }} - {{ order.menuFriendlyName }} - {{ order.orderDate | date : 'EE dd/LL' }}:
              <span class="amount-highlight">{{ order.price | currency }}</span>
            </div>
          </div>
        </mat-expansion-panel>

        <mat-expansion-panel *ngIf="totalFees > 0" [expanded]="true">
          <mat-expansion-panel-header>
            <mat-panel-title>
              <span class="panel-title">Order Fees: (<span class="amount-highlight">{{ totalFees | currency }}</span>)</span>
            </mat-panel-title>
          </mat-expansion-panel-header>
          <div class="panel-content">
            <div *ngFor="let fee of feesToDisplay" class="fee-item">
              Order Fee ({{ fee.name }})
              <span class="amount-highlight">+{{ fee.fee | currency }}</span>
            </div>
          </div>
        </mat-expansion-panel>
      </mat-accordion>

      <!-- Edit Order Summary -->
      <div *ngIf="editOrderSummary" class="edit-summary">
        <div class="summary-line">Previous order: <span class="amount-highlight">{{ editOrderSummary.previousPrice | currency }}</span></div>
        <div class="summary-line">New order: <span class="amount-highlight">{{ editOrderSummary.price | currency }}</span></div>
        <div class="summary-line">Difference: <span class="amount-highlight">{{ editOrderSummary.priceDiff | currency }}</span></div>
      </div>

      <!-- Total -->
      <div class="total-section">
        <span class="total-label">{{ getTextTotalOrder() }}:</span>
        <span class="total-amount">{{ totalPrice | currency }}</span>
      </div>

      <!-- Wallet Balance -->
      <div class="wallet-section">
        <div class="wallet-balance">
          Wallet Balance: <span class="amount-highlight">{{ accountBalance | currency }}</span>
        </div>
        <div class="wallet-description">
          We'll deduct the total orders amount from your wallet balance.
        </div>
      </div>

      <!-- Error Message -->
      <div *ngIf="errorMessage" class="error-message">
        <mat-error>{{ errorMessage }}</mat-error>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button mat-stroked-button class="go-back-btn" (click)="closeDialog()">
          Go back
        </button>
        <button
          mat-raised-button
          color="primary"
          class="confirm-btn"
          (click)="confirmOrder()"
          [disabled]="canteenOrAdminInsufficientWalletBalance || buttonLoading">
          <mat-spinner *ngIf="buttonLoading" diameter="20"></mat-spinner>
          <span *ngIf="!buttonLoading">{{ getConfirmButtonText() }}</span>
          <span *ngIf="buttonLoading">Processing...</span>
        </button>
      </div>
    </div>

    <!-- Order Placed Success -->
    <pos-orders-placed *ngIf="orderPlaced" (goToOrders)="GotToOrders()"></pos-orders-placed>

    <!-- Processing State -->
    <div *ngIf="isProcessing" class="processing-state">
      <h2 class="dialog-title">Processing your order...</h2>
      <div class="loading-state">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Please wait while we process your order...</p>
      </div>
    </div>
  </div>
</mat-dialog-content>
