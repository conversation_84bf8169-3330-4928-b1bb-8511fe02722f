import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import {
  PosMessage,
  PosMessageType,
  createPosMessage,
  isMessageOfType,
  OrderPlacedPayload,
  StudentSelectedPayload,
  MenuChangedPayload,
  CartUpdatedPayload,
  ViewRefreshPayload,
  ConnectionStatusPayload,
  ItemPopupOpenedPayload,
  ItemPopupClosedPayload,
  CategoryChangedPayload,
  BalanceUpdatedPayload,
  CartClearedPayload
} from '../models/pos-messages.interface';

/**
 * Service for handling cross-tab communication in POS system
 * Uses BroadcastChannel API with localStorage fallback for browser compatibility
 */
@Injectable({
  providedIn: 'root'
})
export class PosCommunicationService implements OnDestroy {
  private broadcastChannel: BroadcastChannel | null = null;
  private messageSubject = new Subject<PosMessage>();
  private connectionStatusSubject = new BehaviorSubject<boolean>(false);
  private readonly CHANNEL_NAME = 'pos-communication-channel';
  private readonly STORAGE_KEY = 'pos-cross-tab-message';
  private readonly HEARTBEAT_KEY = 'pos-heartbeat';
  private heartbeatInterval: any;
  private isInitialized = false;

  constructor() {
    this.initializeCommunication();
  }

  /**
   * Initialize communication channels (BroadcastChannel + localStorage fallback)
   */
  private initializeCommunication(): void {
    if (this.isInitialized) {
      return;
    }

    try {
      // Try BroadcastChannel first (modern browsers)
      if ('BroadcastChannel' in window) {
        this.broadcastChannel = new BroadcastChannel(this.CHANNEL_NAME);
        this.broadcastChannel.onmessage = (event) => {
          this.handleIncomingMessage(event.data);
        };
        this.connectionStatusSubject.next(true);
      }
      
      // Setup localStorage fallback for all browsers
      this.setupLocalStorageFallback();
      
      // Setup heartbeat to detect active tabs
      this.setupHeartbeat();
      
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize POS communication:', error);
      this.connectionStatusSubject.next(false);
    }
  }

  /**
   * Setup localStorage event listener as fallback
   */
  private setupLocalStorageFallback(): void {
    window.addEventListener('storage', (event) => {
      if (event.key === this.STORAGE_KEY && event.newValue) {
        try {
          const message: PosMessage = JSON.parse(event.newValue);
          this.handleIncomingMessage(message);
        } catch (error) {
          console.error('Failed to parse localStorage message:', error);
        }
      }
    });
  }

  /**
   * Setup heartbeat mechanism to track active tabs
   */
  private setupHeartbeat(): void {
    // Send heartbeat every 5 seconds
    this.heartbeatInterval = setInterval(() => {
      const heartbeat = {
        timestamp: Date.now(),
        tabId: this.generateTabId()
      };
      localStorage.setItem(this.HEARTBEAT_KEY, JSON.stringify(heartbeat));
    }, 5000);
  }

  /**
   * Generate unique tab identifier
   */
  private generateTabId(): string {
    return `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Handle incoming messages from other tabs
   */
  private handleIncomingMessage(message: PosMessage): void {
    if (this.isValidMessage(message)) {
      this.messageSubject.next(message);
    }
  }

  /**
   * Validate incoming message structure
   */
  private isValidMessage(message: any): message is PosMessage {
    return message && 
           typeof message.type === 'string' && 
           typeof message.timestamp === 'number' && 
           typeof message.sourceGuid === 'string';
  }

  /**
   * Send message to other tabs
   */
  sendMessage(
    type: PosMessageType, 
    payload: any, 
    sourceGuid: string, 
    targetViewType?: 'merchant' | 'student' | 'all'
  ): void {
    const message = createPosMessage(type, payload, sourceGuid, targetViewType);
    
    try {
      // Try BroadcastChannel first
      if (this.broadcastChannel) {
        this.broadcastChannel.postMessage(message);
      } else {
        // Fallback to localStorage
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(message));
        // Immediately remove to trigger storage event
        setTimeout(() => {
          localStorage.removeItem(this.STORAGE_KEY);
        }, 100);
      }
    } catch (error) {
      console.error('Failed to send POS message:', error);
    }
  }

  /**
   * Subscribe to all messages
   */
  onMessage(): Observable<PosMessage> {
    return this.messageSubject.asObservable();
  }

  /**
   * Subscribe to messages of specific type
   */
  onMessageOfType<T>(type: PosMessageType): Observable<PosMessage & { payload: T }> {
    return this.messageSubject.asObservable().pipe(
      filter(message => isMessageOfType<T>(message, type)),
      map(message => message as PosMessage & { payload: T })
    );
  }

  /**
   * Subscribe to messages for specific view type
   */
  onMessageForViewType(viewType: 'merchant' | 'student'): Observable<PosMessage> {
    return this.messageSubject.asObservable().pipe(
      filter(message => 
        !message.targetViewType || 
        message.targetViewType === 'all' || 
        message.targetViewType === viewType
      )
    );
  }

  /**
   * Get connection status observable
   */
  getConnectionStatus(): Observable<boolean> {
    return this.connectionStatusSubject.asObservable();
  }

  /**
   * Check if communication is available
   */
  isConnected(): boolean {
    return this.connectionStatusSubject.value;
  }

  /**
   * Send order placed message
   */
  sendOrderPlaced(payload: OrderPlacedPayload, sourceGuid: string): void {
    this.sendMessage(PosMessageType.ORDER_PLACED, payload, sourceGuid, 'all');
  }

  /**
   * Send student selected message
   */
  sendStudentSelected(payload: StudentSelectedPayload, sourceGuid: string): void {
    this.sendMessage(PosMessageType.STUDENT_SELECTED, payload, sourceGuid, 'student');
  }

  /**
   * Send menu changed message
   */
  sendMenuChanged(payload: MenuChangedPayload, sourceGuid: string): void {
    this.sendMessage(PosMessageType.MENU_CHANGED, payload, sourceGuid, 'student');
  }

  /**
   * Send cart updated message
   */
  sendCartUpdated(payload: CartUpdatedPayload, sourceGuid: string): void {
    this.sendMessage(PosMessageType.CART_UPDATED, payload, sourceGuid, 'student');
  }

  /**
   * Send view refresh message
   */
  sendViewRefresh(payload: ViewRefreshPayload, sourceGuid: string): void {
    this.sendMessage(PosMessageType.VIEW_REFRESH, payload, sourceGuid, 'all');
  }

  /**
   * Send item popup opened message
   */
  sendItemPopupOpened(payload: ItemPopupOpenedPayload, sourceGuid: string): void {
    this.sendMessage(PosMessageType.ITEM_POPUP_OPENED, payload, sourceGuid, 'student');
  }

  /**
   * Send item popup closed message
   */
  sendItemPopupClosed(payload: ItemPopupClosedPayload, sourceGuid: string): void {
    this.sendMessage(PosMessageType.ITEM_POPUP_CLOSED, payload, sourceGuid, 'student');
  }

  /**
   * Send category changed message
   */
  sendCategoryChanged(payload: CategoryChangedPayload, sourceGuid: string): void {
    this.sendMessage(PosMessageType.CATEGORY_CHANGED, payload, sourceGuid, 'student');
  }

  /**
   * Send balance updated message
   */
  sendBalanceUpdated(payload: BalanceUpdatedPayload, sourceGuid: string): void {
    this.sendMessage(PosMessageType.BALANCE_UPDATED, payload, sourceGuid, 'student');
  }

  /**
   * Send cart cleared message
   */
  sendCartCleared(payload: CartClearedPayload, sourceGuid: string): void {
    this.sendMessage(PosMessageType.CART_CLEARED, payload, sourceGuid, 'student');
  }

  /**
   * Cleanup resources
   */
  ngOnDestroy(): void {
    this.destroy();
  }

  /**
   * Destroy service and cleanup resources
   */
  destroy(): void {
    if (this.broadcastChannel) {
      this.broadcastChannel.close();
      this.broadcastChannel = null;
    }
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    this.messageSubject.complete();
    this.connectionStatusSubject.complete();
    this.isInitialized = false;
  }
}
