import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { v4 as uuidv4 } from 'uuid';
import { ActivatedRoute } from '@angular/router';
import { FormGroup, FormControl } from '@angular/forms';

// ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { CanteenState } from '../../../states';
import { canteenStateSelector } from '../../../states/canteen/canteen.selectors';
import * as canteenActions from '../../../states/canteen/canteen.actions';

// Models
import { UserCashless, Canteen, CanteenSchool } from '../../../sharedModels';

@Component({
  selector: 'app-pos',
  templateUrl: './pos.component.html',
  styleUrls: ['./pos.component.scss'],
})
export class PosComponent implements OnInit, OnDestroy {
  SCHOOL_ID = 52243;
  showStudentDropdown = false;
  selectedStudent: UserCashless | null = null;

  // Merchant dropdown properties
  merchantFormGroup: FormGroup;
  listCanteens: Canteen[] = [];
  canteenListVisible: boolean = true;
  currentCanteenId: number;

  // School dropdown properties
  schoolFormGroup: FormGroup;
  listSchools: CanteenSchool[] = [];

  // Subscriptions
  private subscription: Subscription;
  private listLoaded: boolean = false;
  private selectedCanteen: number;

  constructor(
    private route: ActivatedRoute,
    private canteenStore: Store<{ canteen: CanteenState }>
  ) {}

  ngOnInit(): void {
    // Check if we're coming from the canteen/students route
    this.route.url.subscribe(segments => {
      const path = segments.map(segment => segment.path).join('/');
      this.showStudentDropdown = window.location.pathname.includes('canteen/students');
    });

    // Initialize merchant dropdown
    this.subscription = this.canteenStore
      .pipe(select(canteenStateSelector))
      .subscribe((state: CanteenState) => {
        this.currentCanteenId = state?.selected?.CanteenId;
        if (!this.listLoaded) {
          this.listCanteens = [...state.list];

          if (state.dataLoaded) {
            this.listLoaded = true;

            if (this.listCanteens && this.listCanteens.length > 0) {
              this.canteenListVisible = this.listCanteens != null && this.listCanteens.length > 1;
              this.createMerchantForm(state.selected);

              // Initialize school dropdown if merchant is selected
              if (state.selected) {
                this.initializeSchoolDropdown(state);
              }
              return;
            }
          }
        }

        // Update school dropdown when merchant changes
        if (state.selected && state.selected.CanteenId !== this.selectedCanteen) {
          this.selectedCanteen = state.selected.CanteenId;
          this.initializeSchoolDropdown(state);
        }
      });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  openMerchantView(): void {
    this.openPosTab('merchant');
  }

  openStudentView(): void {
    this.openPosTab('student');
  }

  private openPosTab(viewType: 'merchant' | 'student'): void {
    const guid = uuidv4();
    const url = `/canteen/pos/tab?schoolId=${this.SCHOOL_ID}&guid=${guid}&viewType=${viewType}`;

    // Open in new tab
    window.open(url, '_blank');
  }

  onStudentSelected(student: UserCashless): void {
    this.selectedStudent = student;
    if (student) {
      // Open student view with the selected student
      const guid = uuidv4();
      const url = `/canteen/pos/tab?schoolId=${this.SCHOOL_ID}&guid=${guid}&viewType=student&userId=${student.UserId}`;
      window.open(url, '_blank');
    }
  }

  private createMerchantForm(selectedCanteen: Canteen): void {
    let checkExist = -1;

    if (selectedCanteen) {
      checkExist = this.listCanteens.findIndex(x => x.CanteenId == selectedCanteen.CanteenId);
    }

    // check if selected exist in the list
    let selected = this.listCanteens[0].CanteenId;

    if (checkExist >= 0) {
      selected = selectedCanteen.CanteenId;
    }

    this.merchantFormGroup = new FormGroup({
      canteen: new FormControl(selected),
    });

    // set selected
    this.setSelectedCanteen(selected);

    this.canteen.valueChanges.subscribe(val => {
      this.setSelectedCanteen(val);
    });
  }

  private initializeSchoolDropdown(state: CanteenState): void {
    if (state.selected && state.selected.Schools && state.selected.Schools.length > 0) {
      this.listSchools = state.selected.Schools.slice().sort((a, b) => {
        if (a.Name < b.Name) return -1;
        if (a.Name > b.Name) return 1;
        else return 0;
      });
      this.createSchoolForm(state.selectedSchool);
    }
  }

  private createSchoolForm(selectedSchool: CanteenSchool): void {
    let checkExist = -1;

    if (selectedSchool) {
      checkExist = this.listSchools.findIndex(x => x.SchoolId == selectedSchool.SchoolId);
    }

    // check if selected exist in the list
    let selected = this.listSchools[0].SchoolId;

    if (checkExist >= 0) {
      selected = selectedSchool.SchoolId;
    }

    this.schoolFormGroup = new FormGroup({
      school: new FormControl(selected),
    });

    // set selected
    this.setSelectedSchool(selected);

    this.school.valueChanges.subscribe(val => {
      this.setSelectedSchool(val);
    });
  }

  get canteen() {
    return this.merchantFormGroup.get('canteen');
  }

  get school() {
    return this.schoolFormGroup.get('school');
  }

  private setSelectedCanteen(id: number): void {
    let canteen = this.listCanteens.find(x => x.CanteenId == id);
    // dispatch new canteen if new canteen does not match the canteen in state
    if (canteen && canteen.CanteenId != this.currentCanteenId) {
      this.canteenStore.dispatch(canteenActions.SetSelectedCanteen({ canteen: canteen }));
    }
  }

  private setSelectedSchool(id: number): void {
    let school = this.listSchools.find(x => x.SchoolId == id);
    if (school) {
      // Update the SCHOOL_ID when a school is selected
      this.SCHOOL_ID = school.SchoolId;
      this.canteenStore.dispatch(canteenActions.SetSelectedSchool({ school: school }));
    }
  }
}
