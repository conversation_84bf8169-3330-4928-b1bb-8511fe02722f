<div class="pos-orders-placed">
  <!-- Success Animation -->
  <div class="success-animation">
    <div class="circle-loader load-complete">
      <div class="checkmark draw"></div>
    </div>
  </div>

  <!-- Success Message -->
  <div class="success-content">
    <h3>Order Placed Successfully!</h3>
    
    <div class="order-details" *ngIf="orderId">
      <p class="order-id">
        <strong>Order ID:</strong> #{{ orderId }}
      </p>
    </div>

    <div class="payment-info">
      <p class="payment-method">
        <mat-icon>payment</mat-icon>
        <span>Paid with {{ getPaymentMethodDisplayName() }}</span>
      </p>
    </div>

    <p class="success-message">
      {{ getSuccessMessage() }}
    </p>

    <!-- Additional info for merchant view -->
    <div class="merchant-info" *ngIf="viewType === 'merchant'">
      <div class="info-card">
        <mat-icon>info</mat-icon>
        <div class="info-content">
          <p><strong>Next Steps:</strong></p>
          <ul>
            <li>The student view has been automatically updated</li>
            <li>Order confirmation will be sent to the student's email</li>
            <li>You can continue placing orders for other students</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Additional info for student view -->
    <div class="student-info" *ngIf="viewType === 'student'">
      <div class="info-card">
        <mat-icon>email</mat-icon>
        <div class="info-content">
          <p>An email confirmation will be sent shortly with your order details.</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Action Button -->
  <div class="action-section">
    <button 
      type="button" 
      mat-raised-button 
      color="primary"
      (click)="GotToOrders()" 
      class="action-button">
      {{ getButtonText() }}
    </button>
  </div>
</div>
