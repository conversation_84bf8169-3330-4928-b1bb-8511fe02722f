import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'pos-orders-placed',
  templateUrl: './pos-orders-placed.component.html',
  styleUrls: ['./pos-orders-placed.component.scss'],
})
export class PosOrdersPlacedComponent implements OnInit {
  @Input() orderId: number;
  @Input() paymentMethod: string = 'spriggy';
  @Input() viewType: 'merchant' | 'student' = 'merchant';
  @Output() goToOrders: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor() {}

  ngOnInit() {}

  GotToOrders() {
    this.goToOrders.emit(true);
  }

  getPaymentMethodDisplayName(): string {
    switch (this.paymentMethod) {
      case 'spriggy':
        return 'Spriggy Card / Wallet';
      case 'stripe':
        return 'Stripe';
      case 'cash':
        return 'Cash';
      case 'applepay':
        return 'Apple Pay';
      case 'visa':
        return 'Visa';
      default:
        return 'Selected Payment Method';
    }
  }

  getSuccessMessage(): string {
    if (this.viewType === 'merchant') {
      return 'Order has been successfully placed for the student. The order is now processing and will typically be processed within a few moments.';
    } else {
      return 'Your order is now processing and will typically be processed within a few moments. An email will be sent to confirm your order.';
    }
  }

  getButtonText(): string {
    if (this.viewType === 'merchant') {
      return 'Continue';
    } else {
      return 'Go to Orders';
    }
  }
}
