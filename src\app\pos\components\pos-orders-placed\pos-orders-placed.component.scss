.pos-orders-placed {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 400px;

  .success-animation {
    margin-bottom: 24px;

    // Success checkmark animation (reusing from original orders-placed component)
    $brand-success: #5cb85c;
    $loader-size: 3em;
    $check-height: calc($loader-size/2);
    $check-width: calc($check-height/2);
    $check-left: 0.60em;
    $check-thickness: 4px;
    $check-color: $brand-success;

    .circle-loader {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-left: 4px solid $brand-success;
      border-radius: 50%;
      width: $loader-size;
      height: $loader-size;
      animation: load 1.2s linear infinite;
      margin: 0 auto;
      position: relative;

      &.load-complete {
        animation: none;
        border-color: $brand-success;
        transition: border 500ms ease-out;
      }
    }

    .checkmark {
      display: none;

      &.draw:after {
        animation-duration: 800ms;
        animation-timing-function: ease;
        animation-name: checkmark;
        transform: scaleX(-1) rotate(135deg);
      }

      &:after {
        opacity: 1;
        height: $check-height;
        width: $check-width;
        transform-origin: left top;
        border-right: $check-thickness solid $check-color;
        border-top: $check-thickness solid $check-color;
        content: '';
        left: $check-left;
        top: 1.4em;
        position: absolute;
      }
    }

    .load-complete .checkmark {
      display: block;
    }

    @keyframes load {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes checkmark {
      0% {
        height: 0;
        width: 0;
        opacity: 1;
      }
      20% {
        height: 0;
        width: $check-width;
        opacity: 1;
      }
      40% {
        height: $check-height;
        width: $check-width;
        opacity: 1;
      }
      100% {
        height: $check-height;
        width: $check-width;
        opacity: 1;
      }
    }
  }

  .success-content {
    max-width: 500px;
    margin-bottom: 32px;

    h3 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 20px 0;
    }

    .order-details {
      margin-bottom: 16px;

      .order-id {
        font-size: 16px;
        color: #666;
        margin: 0;
        padding: 8px 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        display: inline-block;
      }
    }

    .payment-info {
      margin-bottom: 20px;

      .payment-method {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 14px;
        color: #4caf50;
        font-weight: 500;
        margin: 0;

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }

    .success-message {
      font-size: 16px;
      line-height: 1.5;
      color: #555;
      margin: 0 0 24px 0;
    }

    .merchant-info,
    .student-info {
      .info-card {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        background-color: #e3f2fd;
        border-radius: 8px;
        border-left: 4px solid #2196f3;
        text-align: left;

        mat-icon {
          color: #2196f3;
          font-size: 20px;
          width: 20px;
          height: 20px;
          margin-top: 2px;
        }

        .info-content {
          flex: 1;

          p {
            margin: 0 0 8px 0;
            font-size: 14px;
            color: #333;

            &:last-child {
              margin-bottom: 0;
            }
          }

          ul {
            margin: 8px 0 0 0;
            padding-left: 20px;

            li {
              font-size: 13px;
              color: #555;
              margin-bottom: 4px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }

    .student-info {
      .info-card {
        background-color: #f3e5f5;
        border-left-color: #9c27b0;

        mat-icon {
          color: #9c27b0;
        }
      }
    }
  }

  .action-section {
    .action-button {
      min-width: 180px;
      height: 44px;
      font-size: 14px;
      font-weight: 600;
      border-radius: 6px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

// Responsive design
@media (max-width: 600px) {
  .pos-orders-placed {
    padding: 20px 16px;

    .success-content {
      h3 {
        font-size: 20px;
      }

      .success-message {
        font-size: 14px;
      }

      .merchant-info,
      .student-info {
        .info-card {
          padding: 12px;

          .info-content {
            p {
              font-size: 13px;
            }

            ul li {
              font-size: 12px;
            }
          }
        }
      }
    }

    .action-section {
      .action-button {
        width: 100%;
        max-width: 280px;
      }
    }
  }
}
