import { Component, OnInit, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { Store } from '@ngrx/store';
import { Subscription } from 'rxjs';
import * as _ from 'lodash';

// States
import { FamilyState } from 'src/app/states';
import { ClearDayDetail } from 'src/app/states/family/family.actions';
import { clearAll } from 'src/app/states/shoppingCart/shopping-cart.actions';

// Models
import {
  UserCashless,
  MenuItem,
  FeeRequest,
  FeeToShow,
  CreateOrderRequest,
  CreateOrderInfo,
  BaseComponent,
  OrderItemSummary,
  EditOrderRequest,
  CreateOrdersSummaryRequest,
  CreateOrdersSummary,
  CreateEditSummary,
  CreateEditSummaryRequest,
  CartOption,
  OrdersSummary,
  CartItem,
} from 'src/app/sharedModels';

// Services
import {
  OrderApiService,
  UserService,
  CashlessAppInsightsService,
  PayService,
  AdminService,
} from 'src/app/sharedServices';

// POS Services
import { PosCommunicationService } from '../../services/pos-communication.service';

// Utilities
import { formatDateToUniversal } from 'src/app/utility';

// POS Models
import { OrderPlacedPayload } from '../../models/pos-messages.interface';

export interface PosPlaceOrderDialogData {
  editOrderId: number;
  groupedCarts: CartItem[][];
  // POS-specific properties (optional)
  viewType?: 'merchant' | 'student';
  guid?: string;
  selectedStudent?: any;
  selectedMenuType?: string;
  selectedOrderDate?: Date;
}

@Component({
  selector: 'pos-place-order-dialog',
  templateUrl: './pos-place-order-dialog.component.html',
  styleUrls: ['./pos-place-order-dialog.component.scss'],
})
export class PosPlaceOrderDialogComponent extends BaseComponent implements OnInit, OnDestroy {
  topUpAmount: number;
  errorMessage: string = null;
  orderPlaced: boolean = false;
  isProcessing: boolean = false;
  isTopUp: boolean = false;
  orders: CartItem[][];
  sufficientWalletBalance: boolean = false;
  cartItems: MenuItem[];
  connectedUser: UserCashless;
  canteenOrAdminInsufficientWalletBalance: boolean = false;
  buttonLoading: boolean = false;
  editOrderId: number;
  accountBalance: number;

  totalPrice: number;
  totalFees: number = 0;
  createOrderSummary: CreateOrdersSummary = null;
  editOrderSummary: CreateEditSummary = null;

  fees: FeeRequest[];
  feesToDisplay: FeeToShow[] = [];
  summaryLoading: boolean = false;

  // POS-specific properties
  paymentMethod: string = 'spriggy';
  isPosOrder: boolean = true;
  placedOrderId: number = null;
  viewType: 'merchant' | 'student' = 'merchant';
  guid: string;
  selectedStudent: any;
  selectedMenuType: string;
  selectedOrderDate: Date;

  private subscriptionBalance$: Subscription;

  // Error Messages
  insufficientFundsError =
    'Sorry, this order cannot be completed due to insufficient funds in the user wallet.';
  cannotRetrieveFundsError = 'We are having an issue retrieving your balance, please contact support team.';
  outOfStockError = 'Sorry, one or more items in your order are out of stock and could not be processed.';
  guestUserOrderError = 'Guest users cannot place orders through the POS system. Please select a regular student.';

  constructor(
    public dialogRef: MatDialogRef<PosPlaceOrderDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PosPlaceOrderDialogData,
    public dialog: MatDialog,
    private store: Store<{ family: FamilyState }>,
    private router: Router,
    private orderApiService: OrderApiService,
    private userService: UserService,
    private location: Location,
    private payService: PayService,
    private adminService: AdminService,
    private appInsightsService: CashlessAppInsightsService,
    private posCommunicationService: PosCommunicationService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.subscriptionBalance$?.unsubscribe();
  }

  private initializeComponent(): void {
    // Initialize from dialog data
    this.orders = this.data.groupedCarts;
    this.editOrderId = this.data.editOrderId;
    this.paymentMethod = 'spriggy'; // Only Spriggy payment is supported
    this.viewType = this.data.viewType || 'merchant';
    this.guid = this.data.guid;
    this.selectedStudent = this.data.selectedStudent;
    this.selectedMenuType = this.data.selectedMenuType;
    this.selectedOrderDate = this.data.selectedOrderDate;

    // Validate POS-specific requirements
    if (!this.validatePosRequirements()) {
      return;
    }

    // Get order summary
    if (this.editOrderId) {
      this.getEditOrderSummaryAPI();
    } else {
      this.getOrderSummaryAPI();
    }

    // Get balance using the same logic as the original canteen system
    this.getUpdatedWalletBalance();
  }

  getUpdatedWalletBalance(): void {
    if (this.userService.IsCanteenOrAdmin()) {
      this.adminOrMerchantGetParentBalance();
      return;
    }
    this.getParentBalance();
  }

  adminOrMerchantGetParentBalance(): void {
    const parent = this.adminService.GetParent();
    this.accountBalance = +parent.SpriggyBalance;
  }

  private validatePosRequirements(): boolean {
    if (!this.selectedStudent) {
      this.errorMessage = 'Please select a student before placing an order.';
      return false;
    }

    if (!this.orders || this.orders.length === 0 || this.orders[0].length === 0) {
      this.errorMessage = 'Your cart is empty. Please add items before placing an order.';
      return false;
    }

    // Guest users cannot place orders in POS
    if (this.isGuestUser()) {
      this.errorMessage = this.guestUserOrderError;
      return false;
    }

    return true;
  }

  private isGuestUser(): boolean {
    return this.selectedStudent?.IsGuest === true;
  }

  getParentBalance(): void {
    this.payService.UpdateBalance();
    this.subscriptionBalance$ = this.payService.SubscribeBalanceUpdate().subscribe({
      next: (response: number) => {
        this.accountBalance = response;
      },
      error: error => {
        this.handleErrorFromService(error);
        this.isProcessing = false;
      },
    });
  }

  GetEditSummaryRequest(carts: CartItem[][]): CreateEditSummaryRequest {
    const orderData = carts.map((cartItem: CartItem[]) => {
      return this.getOrderItemSummary(cartItem);
    });

    return { OrderId: this.editOrderId, Items: orderData[0] };
  }

  getOrderSummaryRequest(carts: CartItem[][]): CreateOrdersSummaryRequest {
    const orderData: OrdersSummary[] = carts.map((cartItem: CartItem[]) => {
      return {
        OrderId: 0,
        StudentId: cartItem[0].studentId,
        OrderDate: formatDateToUniversal(cartItem[0].date),
        MenuId: cartItem[0].menuId,
        Items: this.getOrderItemSummary(cartItem),
      };
    });

    return { Orders: orderData };
  }

  getOrderItemSummary(order: CartItem[]): OrderItemSummary[] {
    return order.map(orderItem => ({
      MenuItemId: orderItem.menuItemId,
      MenuItemOptionIds: this.getOrderItemOptionsSummary(orderItem.selectedOptions),
      Quantity: orderItem.quantity,
    }));
  }

  getOrderItemOptionsSummary(selectedOptions: CartOption[]): number[] {
    if (selectedOptions?.length === 0) {
      return [];
    }
    return selectedOptions.map(option => option.menuItemOptionId);
  }

  getSelectedOptionIds(selectedOptions: CartOption[]): number[] {
    return selectedOptions.map(option => option.menuItemOptionId);
  }

  getOrderSummaryAPI(): void {
    this.summaryLoading = true;
    const request = this.getOrderSummaryRequest(this.orders);

    this.orderApiService.getOrderSummary(request).subscribe({
      next: (res: CreateOrdersSummary) => {
        this.createOrderSummary = res;
        this.totalFees = this.createOrderSummary.createOrdersInfo.reduce((prev, next) => prev + next.fee, 0);
        this.totalPrice = this.createOrderSummary.totalAmount + this.totalFees;
        this.summaryLoading = false;
        this.confirmSufficientUserBalance();
      },
      error: error => {
        this.handleOrderSummaryApiError(error);
      },
    });
  }

  getEditOrderSummaryAPI(): void {
    this.summaryLoading = true;
    const request = this.GetEditSummaryRequest(this.orders);

    this.orderApiService.getEditOrderSummary(request).subscribe({
      next: (res: CreateEditSummary) => {
        this.editOrderSummary = res;
        this.summaryLoading = false;
        this.totalPrice = this.editOrderSummary.price;
      },
      error: error => {
        this.handleOrderSummaryApiError(error);
      },
    });
  }

  /**
   * For by Admin/Canteen creating orders
   * Check if user balance is enough to complete payment after order fee is added
   */
  confirmSufficientUserBalance(): void {
    if (!this.userService.IsCanteenOrAdmin() || this.editOrderId) {
      return;
    }
    this.canteenOrAdminInsufficientWalletBalance = this.accountBalance < this.totalPrice;
    this.errorMessage = this.canteenOrAdminInsufficientWalletBalance ? this.insufficientFundsError : null;
  }

  handleOrderSummaryApiError(error: any): void {
    this.closeDialog(true);
    this.handleErrorFromService(error);
  }

  closeDialog(error: boolean = false): void {
    return this.isTopUp ? this.closeTopUp() : this.dialogRef.close(error);
  }

  closeTopUp(): void {
    this.isTopUp = false;
  }

  TopUpAmountChanged(newAmount: number): void {
    this.topUpAmount = newAmount;
  }

  TopUpClick(): void {
    this.isTopUp = true;
  }

  GotToOrders(): void {
    this.dialogRef.close({ success: true, orderId: this.placedOrderId });
  }

  //////////////////////////////////////////////////
  // Place order
  //////////////////////////////////////////////////

  confirmOrder(): void {
    this.appInsightsService.TrackEvent('ClickPlaceOrderPOS', {
      Orders: JSON.stringify(this.orders),
      PaymentMethod: this.paymentMethod,
      ViewType: this.viewType,
      StudentId: this.selectedStudent?.UserId,
    });

    this.buttonLoading = true;
    this.isProcessing = true;
    this.errorMessage = null;

    // Additional POS validation
    if (!this.validateOrderBeforePlacement()) {
      this.buttonLoading = false;
      this.isProcessing = false;
      return;
    }

    if (this.editOrderId) {
      this.placeEditedOrder();
    } else {
      this.placeNewOrder();
    }
  }

  private validateOrderBeforePlacement(): boolean {
    // Validate student selection
    if (!this.selectedStudent) {
      this.errorMessage = 'Please select a student before placing an order.';
      return false;
    }

    // Validate cart
    if (!this.orders || this.orders.length === 0 || this.orders[0].length === 0) {
      this.errorMessage = 'Your cart is empty. Please add items before placing an order.';
      return false;
    }

    // Guest users cannot place orders in POS
    if (this.isGuestUser()) {
      this.errorMessage = this.guestUserOrderError;
      return false;
    }

    // Validate wallet balance for Spriggy payments
    if (this.paymentMethod === 'spriggy' && !this.isGuestUser() && !this.sufficientWalletBalance && !this.userService.IsCanteenOrAdmin()) {
      this.errorMessage = this.insufficientFundsError;
      return false;
    }

    return true;
  }

  placeNewOrder(): void {
    const request: CreateOrderRequest = this.getCreateOrdersRequest(this.orders);
    this.orderApiService.CreateOrders(request).subscribe({
      next: res => {
        this.placedOrderId = res.OrderId;
        this.orderSuccessApiResponse();
      },
      error: error => {
        this.orderErrorApiResponse(error);
      },
    });
  }

  placeEditedOrder(): void {
    const orderId = this.editOrderId;
    const request: EditOrderRequest = { OrderId: orderId, Items: this.processOrderItems(this.orders[0]) };
    this.orderApiService.EditOrder(request).subscribe({
      next: res => {
        this.placedOrderId = res.OrderId;
        this.orderSuccessApiResponse();
      },
      error: error => {
        this.orderErrorApiResponse(error);
      },
    });
  }

  orderSuccessApiResponse(): void {
    this.orderPlaced = true;
    this._clearCart();
    this.store.dispatch(ClearDayDetail());
    this.isProcessing = false;
    this.buttonLoading = false;

    // Send cross-tab communication for POS
    this.sendOrderPlacedMessage();
  }

  orderErrorApiResponse(error: any): void {
    this.handleErrorFromService(error);
    this.orderPlaced = false;
    this.errorMessage = this.WriteError();
    this.isProcessing = false;
    this.buttonLoading = false;
  }

  needToTopUp(): boolean {
    return this.paymentMethod === 'spriggy' &&
           !this.isGuestUser() &&
           this.accountBalance < this.totalPrice &&
           !this.userService.IsCanteenOrAdmin();
  }

  private _clearCart(): void {
    this.store.dispatch(clearAll());
  }

  private sendOrderPlacedMessage(): void {
    if (this.viewType === 'merchant' && this.placedOrderId && this.guid) {
      const payload: OrderPlacedPayload = {
        orderId: this.placedOrderId,
        studentId: this.selectedStudent.UserId,
        studentName: `${this.selectedStudent.FirstName} ${this.selectedStudent.Lastname}`,
        menuType: this.selectedMenuType,
        orderDate: this.selectedOrderDate.toISOString(),
        totalAmount: this.totalPrice,
        itemCount: this.orders[0].length,
        paymentMethod: this.paymentMethod
      };
      this.posCommunicationService.sendOrderPlaced(payload, this.guid);
    }
  }

  ///////////////////////
  // PLACE ORDER REQUEST
  ///////////////////////

  getEditOrderRequest(cartItems: CartItem[][], orderId: number): EditOrderRequest {
    const groupedCartItems = this.groupCartItems(cartItems);
    return { OrderId: orderId, Items: this.processOrderItems(groupedCartItems[0]) };
  }

  getCreateOrdersRequest(cartItems: CartItem[][]): CreateOrderRequest {
    const groupedCartItems = this.groupCartItems(cartItems);
    const ordersRequestList = groupedCartItems.map(item => {
      return this.processOrders(item);
    });

    return { Orders: ordersRequestList };
  }

  groupCartItems(cartData: CartItem[][]): CartItem[][] {
    return Object.values(cartData).map((cartItems: CartItem[]) => {
      return cartItems;
    });
  }

  processOrderItems(cartItems: CartItem[]): OrderItemSummary[] {
    return cartItems.map((item: CartItem) => {
      return {
        MenuItemId: item.menuItemId,
        Quantity: item.quantity,
        MenuItemOptionIds: this.getSelectedOptionIds(item.selectedOptions),
      };
    });
  }

  processOrders(cartItems: CartItem[]): CreateOrderInfo {
    const itemList = this.processOrderItems(cartItems);

    const firstCartItem = cartItems[0];
    return {
      StudentId: firstCartItem.studentId,
      OrderDate: formatDateToUniversal(firstCartItem.date),
      MenuId: firstCartItem.menuId,
      Items: _.clone(itemList),
    };
  }

  // Payment method helpers
  getPaymentMethodDisplayName(): string {
    switch (this.paymentMethod) {
      case 'spriggy':
        return 'Spriggy Card / Wallet';
      case 'stripe':
        return 'Stripe';
      case 'cash':
        return 'Cash';
      case 'applepay':
        return 'Apple Pay';
      case 'visa':
        return 'Visa';
      default:
        return 'Unknown Payment Method';
    }
  }

  isSpriggyPayment(): boolean {
    return this.paymentMethod === 'spriggy';
  }

  showWalletBalance(): boolean {
    return this.isSpriggyPayment() && !this.isGuestUser();
  }
}
