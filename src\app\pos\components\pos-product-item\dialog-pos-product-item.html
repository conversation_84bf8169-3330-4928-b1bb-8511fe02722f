<div *ngIf="imageUrl" class="row no-gutters popUpImage">
  <div class="col-12">
    <img [src]="GetUrlImage()" alt="Item image" (error)="onMainImageError()" async />
  </div>
</div>
<div *ngIf="hasManyImages" class="row">
  <div class="col-12">
    <img
      *ngFor="let img of item.Images"
      [src]="GetPreviewUrl(img.ImageUrl)"
      alt="image item"
      width="70"
      height="55"
      class="previewImage"
      (click)="ClickPreview(img.ImageUrl)"
      (error)="onPreviewImageError(img.ImageUrl)"
    />
  </div>
</div>

<div *ngIf="!imageUrl && isMobile" class="mockImage"></div>

<div *ngIf="isMobile" class="mobileClose">
  <div class="modalClose" (click)="closeDialog()">
    <mat-icon matTooltip="Close modal">close</mat-icon>
  </div>
</div>

<div class="row no-gutters paddingLine">
  <div class="col-12">
    <div *ngIf="item.NutritionalValue">
      <div class="labelWrapper">
        <p
          class="itemDescription"
          [ngClass]="{'red': item.NutritionalColor=== 'Red', 'green': item.NutritionalColor=== 'Green', 'amber': item.NutritionalColor=== 'Amber'}"
        >
          {{item.NutritionalValue}}
        </p>
      </div>
    </div>
    <h5 class="name">{{item.Name}}</h5>

    <p *ngIf="item.Desc" class="desc">{{item.Desc}}</p>

    <div
      *ngIf="item.IsVeg || item.IsVegan || item.IsGF || item.IsHalal || item.IsLactoseFree || item.IsNutsFree"
      class="dietaryInformation"
    >
      <h4>Dietary Information</h4>
      <ul>
        <li *ngIf="item.IsVeg"><mat-icon>done</mat-icon> <span>Vegetarian</span></li>
        <li *ngIf="item.IsVegan"><mat-icon>done</mat-icon> <span>Vegan</span></li>
        <li *ngIf="item.IsHalal"><mat-icon>done</mat-icon> <span>Halal</span></li>
        <li *ngIf="item.IsLactoseFree"><mat-icon>done</mat-icon> <span>Lactose Free</span></li>
        <li *ngIf="item.IsGF"><mat-icon>done</mat-icon> <span>Gluten Free</span></li>
        <li *ngIf="item.IsNutsFree"><mat-icon>done</mat-icon> <span>Nut Free</span></li>
        <li *ngIf="item.IsFastingFriendly"><mat-icon>done</mat-icon> <span>Fasting Friendly</span></li>
        <li *ngIf="item.IsDairyFree"><mat-icon>done</mat-icon> <span>Dairy Free</span></li>
      </ul>
    </div>

    <p>${{item.Price | number: '1.2-2'}}</p>
  </div>
</div>

<div class="row no-gutters paddingLine">
  <div *ngFor="let option of item.Options; index as i" class="col-12">
    <item-option
      [option]="option"
      (optionChanged)="OptionChanged($event)"
      id="item-option-{{i}}"
    ></item-option>
  </div>
</div>

<div *ngIf="IsAvailable" class="row no-gutters justify-content-center paddingLine">
  <div class="col-12 buttonContainer">
    <ng-container *ngIf="!hideButtonQuantity">
      <div class="buttonQuantity" (click)="MinusClick()" id="button-quantity-minus">-</div>
      <div class="quantity">{{item.Quantity}}</div>
      <div class="buttonQuantity" (click)="PlusClick()" id="button-quantity-plus">+</div>
    </ng-container>
    <ng-container *ngIf="hideButtonQuantity"> Quantity: {{item.Quantity}} </ng-container>
  </div>
</div>

<div class="row no-gutters paddingLine">
  <div class="col-12">
    <p *ngIf="checkingAvailability" class="MessageToUserPopUp">Checking availability, please wait</p>

    <button
      *ngIf="!checkingAvailability && IsAvailable"
      [disabled]="CheckButtonDisable()"
      type="button"
      class="PrimaryButton"
      id="add-to-cart-button"
      (click)="closeDialog(item)"
    >
      Add to Cart
    </button>
  </div>
</div>
