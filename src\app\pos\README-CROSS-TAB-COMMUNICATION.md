# POS Cross-Tab Communication

This document explains the cross-tab communication implementation for the POS (Point of Sale) system.

## Overview

The POS system now supports real-time communication between merchant and student views opened in different browser tabs. When changes are made in the merchant view, they are automatically synchronized to the student view.

## Features

### Implemented Communication Types

1. **Order Placement** - When merchant places an order, student view receives notification
2. **Student Selection** - When merchant selects a student, student view shows full menu interface
3. **Menu Changes** - When merchant changes menu type or date, student view is synchronized
4. **Cart Updates** - When merchant adds/removes items, student view shows cart status
5. **Item Popup Synchronization** - When merchant clicks on menu items, student view shows read-only popup

### Visual Indicators

- **Student View**: Shows sync status indicator in the top header
- **Merchant View**: Shows connection status banner when connected to student view
- **Real-time Updates**: Last sync time is displayed

## Technical Implementation

### Architecture

- **BroadcastChannel API**: Primary communication method for modern browsers
- **localStorage Events**: Fallback for broader browser compatibility
- **Type-safe Messages**: All messages use TypeScript interfaces for type safety

### Key Components

1. **PosCommunicationService**: Handles all cross-tab messaging
2. **PosMessage Interface**: Defines message structure
3. **Message Types**: Enum of all supported message types
4. **Payload Interfaces**: Type-safe payloads for each message type

### Browser Compatibility

- **BroadcastChannel**: Chrome 54+, Firefox 38+, Safari 15.4+
- **localStorage Events**: All modern browsers
- **Automatic Fallback**: Service automatically uses localStorage if BroadcastChannel is unavailable

## Testing Instructions

### Setup

1. Start the application
2. Navigate to POS module
3. Open merchant view in one tab
4. Open student view in another tab

### Test Scenarios

#### 1. Test Connection Status

1. Open both merchant and student views
2. Verify connection indicators appear:
   - Student view: Green sync indicator in header
   - Merchant view: Blue connection banner

#### 2. Test Student Selection

1. In merchant view, select a student from dropdown
2. Verify student view shows full menu interface (categories, items, cart)
3. Check that mobile place order button appears in student view
4. Verify last sync time updates
5. Test clearing student selection to see waiting message return

#### 3. Test Menu Changes

1. In merchant view, change menu type (Recess/Lunch)
2. In merchant view, change order date
3. Check browser console in student view for synchronization messages

#### 4. Test Cart Updates

1. In merchant view, add items to cart
2. In merchant view, remove items from cart
3. Check browser console in student view for cart update messages

#### 5. Test Order Placement

1. In merchant view, place an order
2. Check browser console in student view for order completion message
3. Verify menu refreshes if same student/menu type

### Debugging

Enable browser console to see detailed logging:
- `[POS] Setting up cross-tab communication...`
- `[POS] Connection status changed...`
- `[POS] Received message for [view] view...`
- `[POS] Sending [message type] message...`

## Configuration

### Message Types

```typescript
enum PosMessageType {
  ORDER_PLACED = 'ORDER_PLACED',
  STUDENT_SELECTED = 'STUDENT_SELECTED',
  MENU_CHANGED = 'MENU_CHANGED',
  CART_UPDATED = 'CART_UPDATED',
  VIEW_REFRESH = 'VIEW_REFRESH',
  CONNECTION_STATUS = 'CONNECTION_STATUS'
}
```

### Service Configuration

The service is configured with these defaults:
- Channel Name: `'pos-communication-channel'`
- Storage Key: `'pos-cross-tab-message'`
- Heartbeat Interval: 5 seconds

## Future Enhancements

1. **Toast Notifications**: Add user-friendly notifications for cross-tab events
2. **Advanced Synchronization**: Sync more detailed state information
3. **Conflict Resolution**: Handle simultaneous changes from multiple tabs
4. **Performance Optimization**: Implement message throttling for high-frequency updates
5. **Offline Support**: Queue messages when tabs are temporarily disconnected

## Troubleshooting

### Common Issues

1. **No Connection Indicator**: Check if both tabs are from same origin
2. **Messages Not Received**: Verify browser console for errors
3. **Sync Indicator Not Updating**: Check if JavaScript is enabled
4. **Performance Issues**: Monitor message frequency in console

### Browser Support

If BroadcastChannel is not supported, the service automatically falls back to localStorage events. This ensures compatibility with older browsers while providing optimal performance on modern ones.
