@import '../../../../styles/cashless-theme.scss';
@import '../../../../styles/cashless-breakpoints.scss';
@import '../../../../styles/cashless-font.scss';
.itemCol {
  padding: 10px;
  height: auto;
}

.mat-mdc-card {
  border-radius: 8px;
}

.name {
  font-size: 26px;
  font-family: 'bariol_bold';
  margin-top: 5px;
  margin-bottom: 5px;
}
.itemPicture {
  border-radius: 50%;
}
.noPictureItem {
  text-align: center;
  height: 56px;
  width: 56px;
  border-radius: 50%;
  background-color: #e8e8e8;

  p {
    padding: 0;
    margin: 0;
    padding-top: 5px;
  }
}

.itemName {
  font-size: 20px;
  padding-left: 12px;
  font-family: 'bariol_bold';

  @media (max-width: $breakpoint-sm) {
    font-size: 18px;
  }
}

.itemPrice {
  font-size: 16px;
  padding-left: 12px;
  padding-top: 10px;
}

/////////////////////////////////////////
// Dialog CSS
.custom-dialog-container .mat-mdc-dialog-container {
  padding: 0;
}

.buttonContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.mobileClose {
  position: fixed;
  top: 10px;
  left: 5px;
  z-index: 1000;
}

.paddingLine {
  padding: 12px;
}

.buttonQuantity {
  display: inline-block;
  width: 32px;
  height: 32px;
  text-align: center;
  font-size: 28px;
  border-radius: 50%;
  background-color: #e8e8e8;
}

.quantity {
  // display: inline-block;
  text-align: center;
  // width: 40px;
  // height: 40px;
  margin-left: 20px;
  margin-right: 20px;
}

.rowItem {
  @media (min-width: $breakpoint-sm) {
    margin-right: 0px;
    margin-left: 0px;
  }

  @media (max-width: $breakpoint-sm) {
    margin-right: 15px;
    margin-left: 15px;
  }

  &.notAvailable {
    opacity: 0.6;
  }
}

.previewImage {
  padding: 2px;
  border: 1px solid $grey-3;
  margin-left: 10px;
  cursor: pointer;
}

.AddCartButton {
  width: 100%;
  background-color: $green-1;
  color: white;
  margin-top: 10px;
}

// Below is copied from online. Need to fix it later.

.select-css {
  display: block;
  font-size: 12px;
  font-family: sans-serif;
  font-weight: 700;
  color: #444;
  line-height: 1.3;
  padding: 0.6em 1.4em 0.5em 0.8em;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  margin-top: 5px;
  margin-bottom: 5px;
  margin-left: 0px;
  margin-right: 0px;
  border: 1px solid #aaa;
  box-shadow: 0 1px 0 1px rgba(0, 0, 0, 0.04);
  border-radius: 0.5em;
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  background-color: #fff;
  background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%204%205%22%3E%3Cpath%20fill%3D%22%23667189%22%20d%3D%22M2%200L0%202h4zm0%205L0%203h4z%22/%3E%3C/svg%3E'),
    linear-gradient(to bottom, #ffffff 0%, #e5e5e5 100%);
  background-repeat: no-repeat, repeat;
  background-position: right 0.7em top 50%, 0 0;
  background-size: 0.65em auto, 100%;
}
.select-css::-ms-expand {
  display: none;
}
.select-css:hover {
  border-color: #888;
}
.select-css:focus {
  border-color: #aaa;
  box-shadow: 0 0 1px 3px rgba(59, 153, 252, 0.7);
  box-shadow: 0 0 0 3px -moz-mac-focusring;
  color: #222;
  outline: none;
}
.select-css option {
  font-weight: normal;
}

.desc {
  width: 100%;
  white-space: pre-wrap;
}

.dietaryInformation {
  & h4 {
    color: $orange-3;
    margin-bottom: 10px;
  }

  & ul {
    margin-top: 0;
    padding-left: 10px;

    & li {
      list-style-type: none;

      & .mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
        color: $green-1;
        vertical-align: middle;
      }

      & span {
        vertical-align: middle;
        padding-left: 5px;
      }
    }
  }
}

.popUpImage {
  & img {
    width: 100%;
    height: auto;
  }
}

.mockImage {
  height: 80px;
}

.MessageToUserPopUp {
  text-align: center;

  &.outOfStock {
    color: $red-1;
  }
}

.labelWrapper {
  display: flex;
}

.itemDescription {
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 20px;
  border-radius: 24px;
  text-align: center;
  margin: 0;
  padding-top: 4px;
  padding-bottom: 2px;
  padding-right: 15px;
  padding-left: 25px;
  position: relative;
}

.itemDescription.green {
  color: #00ba6b;
  background-color: #00ba6b1f;
}

.itemDescription.amber {
  color: #ff7a00;
  background-color: #f3af001f;
}

.itemDescription.red {
  color: #ff3d00;
  background-color: #ff3d001f;
}

.itemDescription::after {
  content: '';
  top: 9px;
  left: 10px;
  width: 9px;
  height: 9px;
  border-radius: 5px;
  position: absolute;
}

.itemDescription.green::after {
  background-color: #00ba6b;
}

.itemDescription.amber::after {
  background-color: #ff7a00;
}

.itemDescription.red::after {
  background-color: #ff3d00;
}
