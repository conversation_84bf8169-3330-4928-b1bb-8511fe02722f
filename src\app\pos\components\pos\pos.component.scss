.pos-container {
  min-height: 100vh;
  padding: 1rem 1rem;
  display: flex;
  justify-content: center; /* Center horizontally */
  align-items: flex-start; /* Align to top */
}

.pos-header {
  text-align: center;
  margin-bottom: 2rem;
}

.pos-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.pos-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 0;
}

.section {
  background-color: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-wrap: wrap;
  row-gap: 1rem;
  column-gap: 1rem;
  margin-bottom: 2rem;
}

.section > div {
  flex: 1;
}

.mat-form-field {
  width: 100%;
}

.mat-form-field .mat-select-trigger {
  min-height: 40px;
  display: flex;
  align-items: center;
}

.col-12.col-md-10.col-lg-8 {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pos-buttons {
  width: 100%;
  display: flex;
  justify-content: center;
}

.pos-buttons .row {
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
}

.pos-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  height: 200px;
  display: flex;
  align-items: center;
}

.pos-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.pos-card.merchant-card {
  border-left: 4px solid #4CAF50;
}

.pos-card.merchant-card:hover {
  border-left-color: #45a049;
}

.pos-card.student-card {
  border-left: 4px solid #2196F3;
}

.pos-card.student-card:hover {
  border-left-color: #1976D2;
}

.pos-card-content {
  text-align: center;
  width: 100%;
  padding: 1.5rem;
}

.pos-icon {
  font-size: 3rem;
  height: 3rem;
  width: 3rem;
  margin-bottom: 0rem;
}

.merchant-card .pos-icon {
  color: #4CAF50;
}

.student-card .pos-icon {
  color: #2196F3;
}

.pos-card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.pos-card-description {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.4;
  margin-bottom: 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .pos-title {
    font-size: 2rem;
  }

  .pos-card {
    height: 180px;
  }

  .pos-icon {
    font-size: 2.5rem;
    height: 2.5rem;
    width: 2.5rem;
  }

  .pos-card-title {
    font-size: 1.3rem;
  }

  .section {
    flex-direction: column;
  }

  .section > div {
    width: 100%;
  }
}



.mat-mdc-form-field {
  width: 100%!important;
}
