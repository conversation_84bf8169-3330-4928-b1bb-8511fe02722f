@import '../../../../styles/cashless-theme.scss';

.container {
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: fit-content;
  cursor: pointer;
  border-radius: 8px;
  color: $orange-3;
  background-color: white;
  border: 2px transparent solid;

  &.selected {
    border: 2px $orange-1 solid;
  }

  .text {
    width: 80px;
    margin: 0;
    padding: 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: center;
  }
}
