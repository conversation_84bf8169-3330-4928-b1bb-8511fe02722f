import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import * as _ from 'lodash';

// ngrx
import { Subscription } from 'rxjs';
import { Store } from '@ngrx/store';
import { FamilyState } from '../../../states';

// google analytics
declare const gtag: Function;

// Model
import {
  UserCashless,
  MenuItem,
  FeeRequest,
  FeeToShow,
  CreateOrderRequest,
  CreateOrderInfo,
  BaseComponent,
  OrderItemSummary,
  EditOrderRequest,
  CreateOrdersSummaryRequest,
  CreateOrdersSummary,
  CreateEditSummary,
  CreateEditSummaryRequest,
  PlaceOrderDialogData,
  CartOption,
  OrdersSummary,
  CartItem,
} from 'src/app/sharedModels';

// Service
import {
  OrderApiService,
  UserService,
  CashlessAppInsightsService,
  PayService,
  AdminService,
} from 'src/app/sharedServices';
import { ClearDayDetail } from 'src/app/states/family/family.actions';
import { formatDateToUniversal } from 'src/app/utility';
import { clearAll } from 'src/app/states/shoppingCart/shopping-cart.actions';

@Component({
  selector: 'family-dialog-place-order',
  templateUrl: './dialog-place-order.component.html',
  styleUrls: ['./dialog-place-order.component.scss'],
})
export class DialogPlaceOrderComponent extends BaseComponent implements OnInit, OnDestroy {
  topUpAmount: number;
  errorMessage: string = null;
  orderPlaced: boolean = false;
  isProcessing: boolean = false;
  isTopUp: boolean = false;
  orders: CartItem[][];
  sufficientWalletBalance: boolean = false;
  cartItems: MenuItem[];
  connectedUser: UserCashless;
  canteenOrAdminInsufficientWalletBalance: boolean = false;
  buttonLoading: boolean = false;
  editOrderId: number;
  accountBalance: number;

  totalPrice: number;
  totalFees: number = 0;
  createOrderSummary: CreateOrdersSummary = null;
  editOrderSummary: CreateEditSummary = null;

  fees: FeeRequest[];
  feesToDisplay: FeeToShow[] = [];
  summaryLoading: boolean = false;

  private subscriptionBalance$: Subscription;

  //error Messages
  insufficientFundsError =
    'Sorry, this order cannot be completed due to insufficient funds in the user wallet.';
  cannotRetrieveFundsError = 'We are having an issue retrieving your balance, please contact support team.';
  outOfStockError = 'Sorry, one or more items in your order are out of stock and could not be processed.';

  constructor(
    public dialogRef: MatDialogRef<DialogPlaceOrderComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PlaceOrderDialogData,
    public dialog: MatDialog,
    private store: Store<{ family: FamilyState }>,
    private router: Router,
    private orderApiService: OrderApiService,
    private userService: UserService,
    private location: Location,
    private payService: PayService,
    private adminService: AdminService,
    private appInsightsService: CashlessAppInsightsService
  ) {
    super();
  }

  ngOnInit(): void {
    this.connectedUser = this.userService.GetUserConnected();
    this.editOrderId = this.data.editOrderId;
    this.orders = _.cloneDeep(this.data.groupedCarts);

    this.getUpdatedWalletBalance();

    if (this.editOrderId) {
      this.getEditOrderSummaryAPI();
    } else {
      this.getOrderSummaryAPI();
    }
  }

  getUpdatedWalletBalance(): void {
    if (this.userService.IsCanteenOrAdmin()) {
      this.adminOrMerchantGetParentBalance();
      return;
    }
    this.getParentBalance();
  }

  adminOrMerchantGetParentBalance(): void {
    const parent = this.adminService.GetParent();
    this.accountBalance = +parent.SpriggyBalance;
  }

  getParentBalance(): void {
    this.payService.UpdateBalance();
    //gets updated user balance after top up
    this.subscriptionBalance$ = this.payService.SubscribeBalanceUpdate().subscribe({
      next: (response: number) => {
        this.accountBalance = response;
      },
      error: error => {
        this.handleErrorFromService(error);
        this.isProcessing = false;
      },
    });
  }

  ngOnDestroy(): void {
    this.subscriptionBalance$?.unsubscribe();
  }

  GetEditSummaryRequest(carts: CartItem[][]): CreateEditSummaryRequest {
    const orderData = carts.map((cartItem: CartItem[]) => {
      return this.getOrderItemSummary(cartItem);
    });

    return { OrderId: this.editOrderId, Items: orderData[0] };
  }

  topUpMinimumAmount(): number {
    return this.editOrderSummary
      ? this.editOrderSummary.priceDiff
      : this.createOrderSummary.totalAmount - this.createOrderSummary.balance;
  }

  getOrderSummaryRequest(carts: CartItem[][]): CreateOrdersSummaryRequest {
    const orderData: OrdersSummary[] = carts.map((cartItem: CartItem[]) => {
      return {
        OrderId: 0,
        StudentId: cartItem[0].studentId,
        OrderDate: formatDateToUniversal(cartItem[0].date),
        MenuId: cartItem[0].menuId,
        Items: this.getOrderItemSummary(cartItem),
      };
    });

    return { Orders: orderData };
  }

  getOrderItemSummary(order: CartItem[]): OrderItemSummary[] {
    return order.map(orderItem => ({
      MenuItemId: orderItem.menuItemId,
      MenuItemOptionIds: this.getOrderItemOptionsSummary(orderItem.selectedOptions),
      Quantity: orderItem.quantity,
    }));
  }

  getOrderItemOptionsSummary(selectedOptions: CartOption[]): number[] {
    if (selectedOptions?.length === 0) {
      return [];
    }
    return selectedOptions.map(option => option.menuItemOptionId);
  }

  getOrderSummaryAPI(): void {
    this.summaryLoading = true;
    const request = this.getOrderSummaryRequest(this.orders);

    this.orderApiService.getOrderSummary(request).subscribe({
      next: (res: CreateOrdersSummary) => {
        this.createOrderSummary = res;
        this.totalFees = this.createOrderSummary.createOrdersInfo.reduce((prev, next) => prev + next.fee, 0);
        this.totalPrice = this.createOrderSummary.totalAmount + this.totalFees;
        this.summaryLoading = false;
        this.confirmSufficientUserBalance();
      },
      error: error => {
        this.handleOrderSummaryApiError(error);
      },
    });
  }

  getEditOrderSummaryAPI(): void {
    this.summaryLoading = true;
    const request = this.GetEditSummaryRequest(this.orders);

    this.orderApiService.getEditOrderSummary(request).subscribe({
      next: (res: CreateEditSummary) => {
        this.editOrderSummary = res;
        this.summaryLoading = false;
        this.totalPrice = this.editOrderSummary.price;
      },
      error: error => {
        this.handleOrderSummaryApiError(error);
      },
    });
  }

  handleOrderSummaryApiError(error: any): void {
    this.closeDialog(true);
    this.handleErrorFromService(error);
  }

  closeDialog(error: boolean = false): void {
    return this.isTopUp ? this.closeTopUp() : this.dialogRef.close(error);
  }

  TopUpAmountChanged(newAmount: number): void {
    this.topUpAmount = newAmount;
  }

  //////////////////////////////////////////////////
  // View
  //////////////////////////////////////////////////
  GotToOrders(): void {
    this.dialogRef.close();
    this.userService.IsCanteenOrAdmin() ? this.location.back() : this.router.navigate(['family/home']);
  }

  async TopUpClick(): Promise<void> {
    this.isTopUp = true;
  }

  closeTopUp(): void {
    this.isTopUp = false;
    this.topUpAmount = null;
  }

  confirmTopUp(): void {
    this.isTopUp = true;
  }

  /**
   * For by Admin/Canteen creating orders
   * Check if user balance is enough to complete payment after order fee is added
   */
  confirmSufficientUserBalance(): void {
    if (!this.userService.IsCanteenOrAdmin() || this.editOrderId) {
      return;
    }
    this.canteenOrAdminInsufficientWalletBalance = this.accountBalance < this.totalPrice;
    this.errorMessage = this.canteenOrAdminInsufficientWalletBalance ? this.insufficientFundsError : null;
  }

  //////////////////////////////////////////////////
  // Place order
  //////////////////////////////////////////////////

  confirmOrder(): void {
    this.appInsightsService.TrackEvent('ClickPlaceOrder', {
      Orders: JSON.stringify(this.orders),
    });

    this.buttonLoading = true;
    this.isProcessing = true;
    this.errorMessage = null;

    if (this.editOrderId) {
      this.placeEditedOrder();
    } else {
      this.placeNewOrder();
    }
  }

  placeNewOrder(): void {
    const request: CreateOrderRequest = this.getCreateOrdersRequest(this.orders);
    this.orderApiService.CreateOrders(request).subscribe({
      next: res => {
        this.orderSuccessApiResponse();
      },
      error: error => {
        this.orderErrorApiResponse(error);
      },
    });
  }

  placeEditedOrder(): void {
    const orderId = this.editOrderId;
    const request: EditOrderRequest = { OrderId: orderId, Items: this.processOrderItems(this.orders[0]) };
    this.orderApiService.EditOrder(request).subscribe({
      next: res => {
        this.orderSuccessApiResponse();
      },
      error: error => {
        this.orderErrorApiResponse(error);
      },
    });
  }

  orderSuccessApiResponse(): void {
    this.orderPlaced = true;
    this._clearCart();
    this.store.dispatch(ClearDayDetail());
    this.isProcessing = false;
    this.buttonLoading = false;
  }

  orderErrorApiResponse(error: any): void {
    this.handleErrorFromService(error);
    this.orderPlaced = false;
    this.errorMessage = this.WriteError();
    this.isProcessing = false;
    this.buttonLoading = false;
  }

  needToTopUp(): boolean {
    return this.accountBalance < this.totalPrice && !this.userService.IsCanteenOrAdmin();
  }

  private _clearCart(): void {
    this.store.dispatch(clearAll());
  }

  ///////////////////////
  // PLACE ORDER REQUEST
  ///////////////////////

  getEditOrderRequest(cartItems: CartItem[][], orderId: number): EditOrderRequest {
    const groupedCartItems = this.groupCartItems(cartItems);
    return { OrderId: orderId, Items: this.processOrderItems(groupedCartItems[0]) };
  }

  getCreateOrdersRequest(cartItems: CartItem[][]): CreateOrderRequest {
    const groupedCartItems = this.groupCartItems(cartItems);
    const ordersRequestList = groupedCartItems.map(item => {
      return this.processOrders(item);
    });

    return { Orders: ordersRequestList };
  }

  groupCartItems(cartData: CartItem[][]): CartItem[][] {
    return Object.values(cartData).map((cartItems: CartItem[]) => {
      return cartItems;
    });
  }

  processOrderItems(cartItems: CartItem[]): OrderItemSummary[] {
    return cartItems.map((item: CartItem) => {
      return {
        MenuItemId: item.menuItemId,
        Quantity: item.quantity,
        MenuItemOptionIds: this.getSelectedOptionIds(item.selectedOptions),
      };
    });
  }

  processOrders(cartItems: CartItem[]): CreateOrderInfo {
    const itemList = this.processOrderItems(cartItems);

    const firstCartItem = cartItems[0];
    return {
      StudentId: firstCartItem.studentId,
      OrderDate: formatDateToUniversal(firstCartItem.date),
      MenuId: firstCartItem.menuId,
      Items: _.clone(itemList),
    };
  }

  getSelectedOptionIds(selectedOptions: CartOption[]): number[] {
    return selectedOptions.map(option => option.menuItemOptionId);
  }
}
