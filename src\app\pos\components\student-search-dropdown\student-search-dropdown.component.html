<div class="student-search-container">
  <label *ngIf="label" class="search-label">{{ label }}</label>

  <mat-form-field appearance="outline" class="student-search-field">
    <mat-label>{{ placeholder }}</mat-label>
    <input
      type="text"
      matInput
      [formControl]="searchControl"
      [matAutocomplete]="auto"
      class="student-search-input"
    />

    <mat-icon matSuffix *ngIf="isLoading" class="loading-icon">
      <mat-spinner diameter="20"></mat-spinner>
    </mat-icon>

    <mat-icon
      matSuffix
      *ngIf="selectedStudent && !isLoading"
      class="clear-icon"
      (click)="clearSelection()"
    >
      clear
    </mat-icon>

    <mat-icon matSuffix *ngIf="!selectedStudent && !isLoading" class="search-icon">
      search
    </mat-icon>
  </mat-form-field>

  <mat-autocomplete
    #auto="matAutocomplete"
    [displayWith]="displayFn"
    (optionSelected)="onStudentSelected($event.option.value)"
    class="student-autocomplete"
  >
    <mat-option
      *ngFor="let student of filteredStudents | async"
      [value]="student"
      class="student-option"
    >
      <div class="student-option-content">
        <div class="student-name">{{ student.FirstName }} {{ student.Lastname }}</div>
        <div class="student-details">{{ student.ClassName }} - {{ student.SchoolName }}</div>
      </div>
    </mat-option>

    <mat-option *ngIf="(filteredStudents | async)?.length === 0 && searchControl.value?.length >= 2" disabled>
      <div class="no-results">No students found</div>
    </mat-option>
  </mat-autocomplete>
</div>
