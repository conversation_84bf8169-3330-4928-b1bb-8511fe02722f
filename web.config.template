<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- MIME Types for static files -->
        <staticContent>
            <mimeMap fileExtension=".json" mimeType="application/json" />
            <mimeMap fileExtension=".woff" mimeType="application/x-font-woff" />
            <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
        </staticContent>
        
        <!-- URL Rewriting for Angular SPA with Hash Routing -->
        <rewrite>
            <rules>
                <!-- Handle Angular hash routing - redirect all non-file requests to index.html -->
                <rule name="angular hash routes" stopProcessing="true">
                    <match url=".*" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                        <add input="{REQUEST_URI}" pattern="^/(api|assets)" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="/index.html" />
                </rule>
            </rules>
        </rewrite>
        
        <!-- Optional: Content Security Policy -->
        <!-- Uncomment if you need CSP headers -->
        <!--
        <httpProtocol>
            <customHeaders>
                <add name="Content-Security-Policy" value="default-src 'self' 'unsafe-inline' 'unsafe-eval' *; script-src 'self' 'unsafe-inline' 'unsafe-eval' *; img-src 'self' 'unsafe-inline' 'unsafe-eval' *; font-src 'self' * data:" />
            </customHeaders>
        </httpProtocol>
        -->
        
        <!-- Optional: Compression -->
        <!-- Uncomment to enable compression -->
        <!--
        <urlCompression doStaticCompression="true" doDynamicCompression="true" />
        <httpCompression>
            <dynamicTypes>
                <add mimeType="text/*" enabled="true" />
                <add mimeType="message/*" enabled="true" />
                <add mimeType="application/javascript" enabled="true" />
                <add mimeType="application/json" enabled="true" />
                <add mimeType="*/*" enabled="false" />
            </dynamicTypes>
            <staticTypes>
                <add mimeType="text/*" enabled="true" />
                <add mimeType="message/*" enabled="true" />
                <add mimeType="application/javascript" enabled="true" />
                <add mimeType="application/json" enabled="true" />
                <add mimeType="*/*" enabled="false" />
            </staticTypes>
        </httpCompression>
        -->
        
        <!-- Optional: Caching -->
        <!-- Uncomment to set cache headers -->
        <!--
        <staticContent>
            <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="1.00:00:00" />
        </staticContent>
        -->
        
    </system.webServer>
</configuration>
